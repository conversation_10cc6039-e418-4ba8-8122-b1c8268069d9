package igame

import (
	"bytes"
	"encoding/gob"
	"fmt"
	"igame/modules"
	"igameCommon/basic"
	"igameCommon/utils"
	"os"
	"os/exec"
	"strings"
)

func Create(gameID int32, config []byte) {
	m := modules.Factory.Create(gameID)
	m.Generate(config)
	generateGo(gameID, m)
	table := m.PayTable()
	generatePayoutTable(gameID, table)
}

func generateGo(gameID int32, m basic.IModule) {
	fname := fmt.Sprintf("generated/M%d.go", gameID)
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(m)
	if err != nil {
		panic(err)
	}

	txt := fmt.Sprintf(`
	// Code generated by igame-gen. DO NOT EDIT.
	package generated

	import (
		"igameCommon/snapshoot"
	)

	var _ = snapshoot.Save("M%d", M%d)

	func M%d() string {
		return "%s"
	}
	`, gameID, gameID, gameID, utils.CompressAndBase64(buf.Bytes()))
	os.WriteFile(fname, []byte(txt), 0755)
	exec.Command("go", "fmt", fname).Run()
}

func generatePayoutTable(gameID int32, payTable map[int64]basic.ISpin) {
	fname := fmt.Sprintf("generated/data%d.csv", gameID)
	strs := make([]string, 0, len(payTable)+1)
	strs = append(strs, "%d:seed,%d:pay,%s:fcc")
	utils.RangeMapOrdered(payTable, func(id int64, spin basic.ISpin) bool {
		payout := spin.Payout()
		tag := spin.Tags()[0]
		if tag == basic.EnumSpinTag.NORM {
			tag = "#"
		}
		strs = append(strs, fmt.Sprintf("%d,%d,%s", id, payout, tag))
		return false
	})
	utils.LogTo(fname, strings.Join(strs, "\n"))
}
