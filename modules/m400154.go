package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

type c400154 struct {
	GameID           int // 游戏ID
	RuleID           int // 生成器规则ID
	Row              int // 网格行数
	Column           int // 网格列数
	MaxPayout        int32
	MaxMul           int
	WildIcon         int16
	ScatterIcon      int16
	DynamiteIcon     int16
	DynamiteMultiMap map[int]int
	ShotProb         int
	PayoutTable      map[int16][]int64
	IconWeight       map[int16]int32
	FreeIconWeight   map[int16]int32
	BoomIconWeight   map[int16]int16
	HotIconWeight    map[int16]int32
	Pattern          [][]basic.Position
	FreeSpin         map[int]int
}

// Example General

var _ = Factory.reg(basic.NewGeneral[*m400154])

type m400154 struct {
	Config                         c400154
	RandNormByWeight               *utils.RandomWeightPicker[int16, int32]
	RandNormByWeightWithoutScatter *utils.RandomWeightPicker[int16, int32]
	RandNormByWeightWithoutWild    *utils.RandomWeightPicker[int16, int32]
	RandNormByWeightWithoutOther   *utils.RandomWeightPicker[int16, int32]
	RandByFreeIconWeight           *utils.RandomWeightPicker[int16, int32]
}

func (m *m400154) Init(config []byte) {
	m.Config = utils.ParseYAML[c400154](config)
	m.RandNormByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	notScatter := func(icon int16) bool {
		return icon != m.Config.ScatterIcon
	}
	noWild := func(icon int16) bool {
		return icon != m.Config.WildIcon
	}
	notOther := func(icon int16) bool {
		return icon != m.Config.ScatterIcon && icon != m.Config.WildIcon && icon != m.Config.DynamiteIcon
	}
	m.RandNormByWeightWithoutScatter = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, notScatter))
	m.RandNormByWeightWithoutWild = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, noWild))
	m.RandNormByWeightWithoutOther = utils.NewRandomWeightPicker(utils.FilterIconWeight(m.Config.IconWeight, notOther))
	m.RandByFreeIconWeight = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)
}

func (m400154) ID() int32 {
	return 400154
}

func (m m400154) Line() int32 { // 线数
	return 20
}

func (m m400154) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400154) Exception(code int32) string {
	return "{}"
}

func (m *m400154) genGrid(rd *rand.Rand) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)
	scatterCount := 0
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if scatterCount >= 3 {
				grid[index] = m.RandNormByWeightWithoutScatter.One(rd)
			} else {
				grid[index] = m.RandNormByWeight.One(rd)
			}

			// 第一列不生成 wild 图标
			if col == 0 {
				grid[index] = m.RandNormByWeightWithoutWild.One(rd)
			}

			if grid[index] == m.Config.ScatterIcon {
				scatterCount++
			}
		}
	}
	return grid
}

func (m *m400154) genFreeBox(rd *rand.Rand, oldGrid []int16, count int16) ([]int16, int16, int, int, int) {
	boxCur := make([]int16, m.Config.Row*m.Config.Column)
	copy(boxCur, oldGrid)
	icons := m.RandByFreeIconWeight.More(m.Config.Row*m.Config.Column, rd)
	addSpin := 0
	bulletCount := 0
	addMul := false
	Mul := 0
	for i, icon := range oldGrid {
		if icon == 0 {
			boxCur[i] = icons[i]
		}
		if boxCur[i] == 99 {
			count++
			addMul = true
			boxCur[i] = m.Config.BoomIconWeight[count]
		}
		if boxCur[i] == -1 || boxCur[i] == -3 {
			addSpin -= int(boxCur[i])
		}
		if boxCur[i] == -5 {
			bulletCount++
		}
	}
	if addMul {
		for _, v := range boxCur {
			if v > 0 {
				Mul += int(v)
			}
		}
	}
	return boxCur, count, addSpin, bulletCount, Mul

}

func (m *m400154) Spin(rd *rand.Rand) basic.ISpin {
	spin := games.S400154{}
	grid := m.genGrid(rd)
	var page games.P400154
	if rd.Intn(100) < m.Config.ShotProb {
		shotSymbol := m.RandNormByWeightWithoutOther.One(rd)
		gridMask := m.genMask(grid, rd, shotSymbol)
		page = games.P400154{
			Grid:       grid,
			ShotSymbol: shotSymbol,
			GridMask:   gridMask,
		}
		spin.IsShot = true
		page.Lines = m.parsePayout(page.GridMask, &spin, &page)
		spin.Pages = append(spin.Pages, page)
	} else {
		page = games.P400154{
			Grid: grid,
		}
		page.Lines = m.parsePayout(page.Grid, &spin, &page)
		spin.Pages = append(spin.Pages, page)
	}

	if spin.IsFree || spin.IsHot {
		boomIconCount := int16(0)
		bullIconCount := 0
		totalSpin := spin.FreeInfo.Total
		for i := 0; i <= totalSpin; i++ {
			freePage := m.genFreePage(rd, spin.Pages[len(spin.Pages)-1], i, boomIconCount)
			totalSpin += freePage.FreeSpin.AddSpin
			boomIconCount = freePage.FreeSpin.BoomIconCount
			freePage.SubGameBonus2Info = m.genSubGameInfo("bonus2", grid, int(freePage.Pays))
			freePage.SubGameBonus2Info.TotalSpins = totalSpin
			bullIconCount += freePage.FreeSpin.BulletCount
			freePage.SubGameBonus2Info.AmountBullets = bullIconCount
			freePage.SubGameBonus2Info.CurWin = int(freePage.Pays)
			freePage.SubGameBonus2Info.RespinRemain = totalSpin - i
			if i == totalSpin {
				mul, shotList := m.genFinMul(rd, freePage.Bonus.BoxCur, freePage.Bonus.NewMult, freePage.SubGameBonus2Info.AmountBullets)
				bonus5Mul := mul - freePage.Bonus.NewMult
				freePage.SubGameBonus2Info.CurWin = mul * int(m.Line())
				freePage.FreeSpin.ShotList = shotList
				freePage.SubGameBonus4Info = m.genSubGameInfo("bonus4", freePage.Grid, freePage.Bonus.NewMult*int(m.Line()))
				freePage.SubGameBonus5Info = m.genSubGameInfo("bonus5", freePage.Grid, bonus5Mul*int(m.Line()))
			}
			spin.Pages = append(spin.Pages, freePage)
		}
		lastFreePage := m.genLastPage(rd, spin.Pages[len(spin.Pages)-1])
		spin.Pages = append(spin.Pages, lastFreePage)
	}

	for _, page := range spin.Pages {
		spin.Pays += page.Pays + page.SubGamePays
	}
	return &spin
}

func (m *m400154) genFreePage(rd *rand.Rand, lastPage games.P400154, i int, lastPageBoomIconCount int16) games.P400154 {
	if i == 0 {
		BoxPrev := make([]int16, m.Config.Row*m.Config.Column)
		gridHoldShot := make([]int16, m.Config.Row*m.Config.Column)
		boxCur, boomIconCount, addSpin, bulletCount, multCount := m.genFreeBox(rd, gridHoldShot, lastPageBoomIconCount)
		return games.P400154{
			Grid:     lastPage.Grid,
			GridMask: lastPage.GridMask,
			Pays:     0,
			Bonus: belatra.Bonus{
				BoxPrev:    BoxPrev,
				BoxCur:     boxCur,
				ExtraSpins: addSpin,
				PrevMult:   0,
				NewMult:    multCount,
			},
			FreeSpin: games.FreeSpin{
				BoomIconCount: boomIconCount,
				AddSpin:       addSpin,
				BulletCount:   bulletCount,
			},
		}
	}
	gridHoldShot := m.genNewCurBox(lastPage.Bonus.BoxCur)
	boxCur, boomIconCount, addSpin, bulletCount, multCount := m.genFreeBox(rd, gridHoldShot, lastPageBoomIconCount)
	mul := multCount + lastPage.Bonus.NewMult
	return games.P400154{
		Pays:     0,
		Grid:     lastPage.Grid,
		GridMask: lastPage.GridMask,
		Bonus: belatra.Bonus{
			BoxPrev:    lastPage.Bonus.BoxCur,
			BoxCur:     boxCur,
			ExtraSpins: addSpin,
			PrevMult:   lastPage.Bonus.NewMult,
			NewMult:    mul,
		},
		FreeSpin: games.FreeSpin{
			BoomIconCount: boomIconCount,
			AddSpin:       addSpin,
			BulletCount:   bulletCount,
		},
	}
}

func (m *m400154) genLastPage(rd *rand.Rand, freePage games.P400154) games.P400154 {
	mul, shotList := m.genFinMul(rd, freePage.Bonus.BoxCur, freePage.Bonus.NewMult, freePage.SubGameBonus2Info.AmountBullets)
	subGameBonus2Info := freePage.SubGameBonus2Info
	subGameBonus2Info.RespinRemain = -1
	subGameBonus2Info.PaidWin = mul * int(m.Line())
	return games.P400154{
		Grid:     freePage.Grid,
		GridMask: freePage.GridMask,
		Pays:     int32(mul) * m.Line(),
		FreeSpin: games.FreeSpin{
			ShotList: shotList,
		},
		Bonus: belatra.Bonus{
			BoxPrev:    m.genNewCurBox(freePage.Bonus.BoxCur),
			BoxCur:     m.genNewCurBox(freePage.Bonus.BoxCur),
			ExtraSpins: freePage.Bonus.ExtraSpins,
			PrevMult:   freePage.Bonus.PrevMult,
			NewMult:    mul,
		},
		SubGameBonus2Info: subGameBonus2Info,
		SubGameBonus4Info: freePage.SubGameBonus4Info,
		SubGameBonus5Info: freePage.SubGameBonus5Info,
	}
}

func (m *m400154) genFinMul(rd *rand.Rand, boxCur []int16, prevMult, bulletCount int) (int, []int) {
	shotList := make([]int, len(boxCur))
	shotBoxCur := make([]int16, len(boxCur))
	copy(shotBoxCur, boxCur)
	for i := 0; i < bulletCount; i++ {
		// 随机选择一个格子进行命中
		targetIndex := rd.Intn(len(boxCur))
		shotList[targetIndex] += 1
		if shotBoxCur[targetIndex] > 0 {
			shotBoxCur[targetIndex] *= 2
		}
		if shotBoxCur[targetIndex] > 0 {
			for _, value := range shotBoxCur {
				prevMult += int(value)
			}
		}
		//prevMult += int(boxCur[targetIndex])
		//mul 最大为12500
		if prevMult >= int(m.Config.MaxPayout) {
			return 12500, shotList
		}
	}
	return prevMult, shotList
}

func (m *m400154) genNewCurBox(gridHoldShot []int16) []int16 {
	newGridHoldShot := make([]int16, len(gridHoldShot))
	for k, v := range gridHoldShot {
		if v < 0 {
			newGridHoldShot[k] = 0
		} else {
			newGridHoldShot[k] = gridHoldShot[k]
		}
	}
	return newGridHoldShot
}

func (m *m400154) genMask(grid []int16, rd *rand.Rand, symbol int16) []int16 {
	mask := make([]int16, len(grid))
	for pos, val := range grid {
		if rd.Intn(100) < 50 {
			if val == m.Config.ScatterIcon || val == m.Config.DynamiteIcon {
				continue
			}
			mask[pos] = symbol
		} else {
			mask[pos] = val
		}
	}

	return mask
}

func (m *m400154) parsePayout(grid []int16, s *games.S400154, page *games.P400154) []belatra.LinesInfo {
	gridRows := make([][]int16, m.Config.Row)
	dynamiteCount := 0
	scatterCount := 0
	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			val := grid[index]
			if val == m.Config.DynamiteIcon {
				dynamiteCount++
			}
			if val == m.Config.ScatterIcon {
				scatterCount++
			}
			gridRows[row][col] = val
		}
	}

	linesInfo := []belatra.LinesInfo{}
	lines := m.checkLine(gridRows, m.Config.Row, m.Config.Column, m.Config.WildIcon)
	for i, line := range lines {
		mul := 1
		online := []int16{}
		for _, rowIdx := range line.OnLine {
			if rowIdx != 127 {
				online = append(online, rowIdx)
			}
		}
		payout := m.Config.PayoutTable[line.Icon][len(online)]
		page.Pays += int32(payout) * int32(mul)
		linesInfo = append(linesInfo, belatra.LinesInfo{
			ID: i,
			Iwin: belatra.Iwin{
				Cost:   payout,
				K:      mul,
				Win:    payout * int64(mul),
				OnLine: online,
			},
		})
	}
	if scatterCount >= 3 {
		s.FreeInfo.Total = int(m.Config.FreeSpin[scatterCount])
	}
	if scatterCount >= 3 && scatterCount < 5 {
		s.IsFree = true
	} else if scatterCount == 5 {
		s.IsHot = true
	}
	if dynamiteCount >= 4 {
		subGameInfo := m.genSubGameInfo("bonus3", grid, int(s.Pays))
		page.SubGameBonus3Info = append(page.SubGameBonus3Info, subGameInfo)
		s.IsDynamites = true
		page.SubGamePays += int32(subGameInfo.CurWin)
	}

	return linesInfo
}

func (m *m400154) genSubGameInfo(game string, grid []int16, startWin int) belatra.SubGameInfo {
	switch game {
	case "bonus3":
		win := 0
		count := 0
		for _, icon := range grid {
			if icon == m.Config.DynamiteIcon {
				count++
				win += m.Config.DynamiteMultiMap[count]
			}
		}
		return belatra.SubGameInfo{
			Category:      "Bonus3",
			Type:          "3",
			StartWin:      startWin,
			PrevWin:       0,
			CurWin:        win,
			PaidWin:       win,
			Attempt:       0,
			Av:            []int{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonus3",
			Add:           map[string]any{},
			OnlyToBD:      nil,
		}
	case "bonus2":
		return belatra.SubGameInfo{
			Category:      "Bonus2",
			Type:          "2",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        startWin,
			PaidWin:       startWin,
			Attempt:       0,
			Av:            []int{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "",
			Add:           map[string]any{},
			OnlyToBD:      nil,
			SendRestore:   nil,
			UserChoice:    "start",
			WillRespin:    false,
			IdTbl:         1,
			AmountBullets: 0,
			WinByBullets:  0,
			RespinRemain:  0,
			TotalSpins:    0,
		}
		// winByLastPageTNT
	case "bonus4":
		return belatra.SubGameInfo{
			Category:      "Bonus4",
			Type:          "4",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        startWin,
			PaidWin:       startWin,
			Attempt:       0,
			Av:            []int{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonus4",
			Add:           map[string]interface{}{},
			OnlyToBD:      nil,
		}
	// winByBullets
	case "bonus5":
		return belatra.SubGameInfo{
			Category:      "Bonus5",
			Type:          "5",
			StartWin:      0,
			PrevWin:       startWin,
			CurWin:        startWin,
			PaidWin:       0,
			Attempt:       0,
			Av:            []int{},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "inBonus5",
			Add:           map[string]interface{}{},
			OnlyToBD:      nil,
		}
	default:
		return belatra.SubGameInfo{}
	}
}

// WinningLine 表示一条中奖线
type WinningLine struct {
	Icon   int16   // 中奖图标
	Length int     // 中奖长度
	OnLine []int16 // 每列的行索引，127表示未参与
}

func (m *m400154) checkLine(gridRows [][]int16, rows, cols int, wildIcon int16) []WinningLine {
	var allPaths []WinningLine
	maxLength := 0

	for startRow := 0; startRow < rows; startRow++ {
		startIcon := gridRows[startRow][0]

		// 跳过scatter图标
		if startIcon == 0 || startIcon == 2 {
			continue
		}
		mainIcon := startIcon
		if startIcon == wildIcon {
			mainIcon = -1
		}

		// 临时存储当前起始行的路径
		var tempResults []WinningLine
		m.findPaths(gridRows, startRow, 0, []int16{int16(startRow)}, []int16{startIcon},
			mainIcon, wildIcon, rows, cols, &tempResults)

		// 保留所有路径和最长长度
		for _, line := range tempResults {
			if line.Icon != -1 && line.Icon != 0 { // 排除scatter
				allPaths = append(allPaths, line)
				if line.Length > maxLength {
					maxLength = line.Length
				}
			}
		}
	}

	// 只保留最长长度的路径
	var results []WinningLine
	for _, line := range allPaths {
		if line.Length == maxLength {
			results = append(results, line)
		}
	}

	return results
}

// findPaths 递归搜索中奖路径
func (m *m400154) findPaths(gridRows [][]int16, currentRow, currentCol int, path []int16, icons []int16,
	mainIcon, wildIcon int16, rows, cols int, results *[]WinningLine) {

	if len(path) >= 3 {
		finalMainIcon := mainIcon
		if finalMainIcon == -1 {
			finalMainIcon = m.getMainIcon(icons, wildIcon)
		}

		if finalMainIcon != -1 && finalMainIcon != 0 { // 排除scatter
			// 创建OnLine数组
			onLine := make([]int16, 5)
			for i := 0; i < 5; i++ {
				onLine[i] = 127 // 默认值表示未参与
			}
			for i, rowIdx := range path {
				if i < len(onLine) {
					onLine[i] = rowIdx
				}
			}

			*results = append(*results, WinningLine{
				Icon:   finalMainIcon,
				Length: len(path),
				OnLine: onLine,
			})
		}
	}

	if currentCol >= cols-1 {
		return
	}

	nextCol := currentCol + 1
	for nextRow := 0; nextRow < rows; nextRow++ {
		nextIcon := gridRows[nextRow][nextCol]

		// 检查是否可以连接
		if m.canConnectIcon(icons, nextIcon, mainIcon, wildIcon) {
			// 更新主图标
			newMainIcon := mainIcon
			if newMainIcon == -1 && nextIcon != wildIcon {
				newMainIcon = nextIcon
			}

			// 创建新路径
			newPath := make([]int16, len(path)+1)
			copy(newPath, path)
			newPath[len(path)] = int16(nextRow)

			newIcons := make([]int16, len(icons)+1)
			copy(newIcons, icons)
			newIcons[len(icons)] = nextIcon

			// 递归搜索
			m.findPaths(gridRows, nextRow, nextCol, newPath, newIcons,
				newMainIcon, wildIcon, rows, cols, results)
		}
	}
}

// canConnectIcon 检查新图标是否可以连接到当前路径
func (m *m400154) canConnectIcon(currentIcons []int16, nextIcon, mainIcon, wildIcon int16) bool {
	// 如果下一个图标是scatter，不能连接
	if nextIcon == 0 || nextIcon == 2 {
		return false
	}

	// 如果还没有确定主图标
	if mainIcon == -1 {
		if nextIcon == wildIcon {
			return true
		}
		return true
	}

	return nextIcon == mainIcon || nextIcon == wildIcon
}

// getMainIcon 从图标序列中获取主图标
func (m *m400154) getMainIcon(icons []int16, wildIcon int16) int16 {
	for _, icon := range icons {
		if icon != wildIcon {
			return icon
		}
	}
	// 如果全是通配符，返回通配符
	if len(icons) > 0 {
		return wildIcon
	}
	return -1
}

func (m *m400154) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m400154) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400154)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	return s
}

func (m400154) Rule() string {
	gs := map[string]any{
		"gamegroup":             "base",
		"doubleAssortment":      []string{"off"},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         []int{1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 50, 75, 100},
		"denomAssortment_cents": []int{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]any{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus": map[string]any{
			"wasBuy":   0,
			"selectId": -1,
			"buyTotalBetK": []map[string]any{
				{"id": 0, "cost": 100, "prefix2": "_BASE_FG", "rtp": 96.23},
				{"id": 1, "cost": 500, "prefix2": "_BASE_FG_HOT", "rtp": 96.25},
				{"id": 2, "cost": 25, "prefix2": "_BASE_SHOTS", "rtp": 96.15},
				{"id": 3, "cost": 15, "prefix2": "_BASE_TNT", "rtp": 96.1},
			},
		},
		"outRatesVolatility":    nil,
		"placedbet":             20,
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            20,
		"betPerLine":            1,
		"nlines":                20,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 2000,
		"analInfo": map[string]any{
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles":  nil,
			"symbolNames": []string{"scatter", "wild", "dynamite", "woman", "star", "revolver", "watch", "whiskey", "a", "k", "q", "j", "incutSymbol"},
			"baseReels": [][]int{
				{5, 0, 10, 9, 5, 5, 10, 5, 8, 0, 10, 3, 9, 4, 5, 10, 3, 10, 8, 8, 0, 9, 7, 7, 11, 4, 8, 10, 6, 6, 9, 0, 10, 10, 7, 9, 8, 7, 0, 3, 9, 0, 10, 7, 4, 8, 2, 2, 9, 4, 0, 10, 7},
				{5, 3, 9, 10, 10, 4, 0, 1, 5, 10, 4, 10, 5, 10, 2, 10, 4, 4, 5, 4, 6, 9, 6, 11, 4, 9, 0, 10, 8, 9, 9, 9, 4, 9, 3, 9, 4, 10, 9, 3, 1, 7, 10, 4, 3, 3, 10, 0, 8, 8, 3, 10, 5, 8, 1, 2, 1, 9, 6, 7, 10, 0, 1, 10},
				{8, 1, 3, 2, 8, 9, 4, 3, 2, 5, 1, 7, 1, 1, 8, 7, 9, 0, 2, 2, 10, 5, 10, 7, 10, 8, 7, 5, 8, 10, 3, 0, 10, 1, 5, 4, 7, 10, 11, 0, 6, 2, 8, 4, 10, 6, 6, 5, 2, 10, 5, 6, 9, 8, 0, 7, 10, 7, 5, 1, 9, 7, 6, 3, 7, 4, 10, 6, 0, 10, 5},
				{7, 8, 2, 2, 0, 4, 5, 5, 4, 9, 10, 1, 0, 1, 9, 2, 9, 8, 9, 2, 2, 0, 4, 10, 10, 8, 5, 7, 10, 8, 1, 11, 0, 8, 2, 9, 5, 6, 9, 6, 8, 8, 6, 8, 0, 10, 7, 9, 9, 3, 2, 10, 3, 6, 4, 9, 8, 8, 6, 0, 9, 9, 8, 10, 10, 9, 9, 4},
				{9, 10, 10, 5, 8, 5, 0, 8, 8, 5, 10, 9, 2, 9, 0, 2, 7, 7, 9, 9, 1, 8, 10, 8, 9, 1, 9, 7, 8, 0, 10, 8, 9, 5, 8, 10, 11, 4, 5, 8, 0, 9, 3, 8, 10, 8, 1, 9, 8, 9, 0, 1, 4, 9, 6, 10, 9, 9, 7, 10, 10, 5, 0, 6, 5, 10, 10, 8, 10},
			},
			"freeReels": [][]int{
				{1, 5, 8, 10, 9, 5, 5, 10, 5, 8, 6, 10, 1, 3, 9, 4, 5, 10, 3, 10, 8, 8, 9, 9, 7, 7, 4, 8, 10, 6, 6, 9, 1, 7, 10, 10, 7, 9, 8, 7, 2, 3, 9, 7, 10, 7, 4, 8, 2, 2, 9, 4, 1, 10, 10, 7},
				{5, 3, 9, 10, 10, 4, 10, 1, 5, 10, 4, 10, 5, 10, 2, 10, 4, 4, 5, 4, 6, 9, 6, 4, 9, 5, 10, 8, 9, 9, 9, 4, 9, 3, 9, 4, 10, 9, 3, 1, 7, 10, 4, 3, 3, 10, 2, 8, 8, 3, 10, 5, 8, 1, 2, 1, 9, 6, 7, 10, 7, 1, 10},
				{8, 1, 3, 2, 8, 9, 4, 3, 2, 5, 1, 7, 1, 1, 8, 7, 9, 3, 2, 2, 10, 5, 10, 7, 10, 8, 7, 5, 8, 10, 3, 10, 10, 1, 5, 4, 7, 10, 8, 6, 2, 8, 4, 10, 6, 6, 5, 2, 10, 5, 6, 9, 8, 6, 7, 10, 7, 5, 1, 9, 7, 6, 3, 7, 4, 10, 6, 4, 10, 5},
				{7, 8, 2, 2, 4, 4, 5, 5, 4, 9, 10, 1, 3, 1, 9, 2, 9, 8, 9, 2, 2, 6, 4, 10, 10, 8, 5, 7, 10, 8, 1, 10, 8, 2, 9, 5, 6, 9, 6, 8, 8, 6, 8, 9, 10, 7, 9, 9, 3, 2, 10, 3, 6, 4, 9, 8, 8, 6, 8, 9, 9, 8, 10, 10, 9, 9, 4},
				{9, 10, 10, 5, 8, 5, 7, 8, 8, 5, 10, 9, 2, 9, 7, 2, 7, 7, 9, 9, 1, 8, 10, 8, 9, 1, 9, 7, 8, 3, 10, 8, 9, 5, 8, 10, 4, 5, 8, 10, 9, 3, 8, 10, 8, 1, 9, 8, 9, 8, 1, 4, 9, 6, 10, 9, 9, 7, 10, 10, 5, 1, 6, 5, 10, 10, 8, 10},
			},
			"statTablo": map[string]any{
				"volatility": 8,
				"bigwin":     9,
				"epicwin":    9,
				"bonus":      2,
				"show":       1,
				"rtp":        96.05,
			},
			"maxWinFreq_big":      23120460,
			"VIP_maxWinFreq_big":  10833558,
			"arrlimits_winLimitK": []int{12500},
			"dynamitesXbet":       []int{1, 1, 1, 2, 3, 4, 5, 8, 10, 20, 30, 50, 100, 300, 500},
			"scatterIds":          []int{0},
			"wildIds":             []int{1},
			"minScatters":         []int{3},
			"bonusValuesBase":     []int{0, 1, -1, -3},
			"bonusValuesSuper":    []int{0, 1, -1, -3},
			"outRates_vipmode":    96.22,
			"volatility":          4,
			"sasAdditionalId":     "LUB",
			"sasPaytableId":       "LUB960",
		},
		"helpInfo": map[string]any{
			"paytable": [][]any{
				{[]int{0, 8}},
				{[]int{1, 1}},
				{[]int{2, 8}},
				{
					[]int{3, 4},
					[]int{5, 500},
					[]int{4, 100},
					[]int{3, 50},
				},
				{
					[]int{4, 4},
					[]int{5, 400},
					[]int{4, 80},
					[]int{3, 40},
				},
				{
					[]int{5, 4},
					[]int{5, 250},
					[]int{4, 50},
					[]int{3, 25},
				},
				{
					[]int{6, 4},
					[]int{5, 100},
					[]int{4, 20},
					[]int{3, 10},
				},
				{
					[]int{7, 4},
					[]int{5, 80},
					[]int{4, 15},
					[]int{3, 8},
				},
				{
					[]int{8, 4},
					[]int{5, 50},
					[]int{4, 10},
					[]int{3, 5},
				},
				{
					[]int{9, 4},
					[]int{5, 50},
					[]int{4, 10},
					[]int{3, 5},
				},
				{
					[]int{10, 4},
					[]int{5, 50},
					[]int{4, 10},
					[]int{3, 5},
				},
				{
					[]int{11, 4},
					[]int{5, 50},
					[]int{4, 10},
					[]int{3, 5},
				},
				{[]int{12, 16}},
			},
			"doubles": [][]any{
				{"off", 0, 0},
			},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"antiDynamiteBet":        nil,
		"dramshow":               nil,
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         12500,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 12500,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []int{20},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox": [][]int{
			{8, 1, 1, 10, 11},
			{8, 8, 3, 10, 4},
			{5, 4, 8, 10, 8},
		},
		"stopBox": [][]int{
			{8, 1, 1, 10, 11},
			{8, 8, 3, 10, 4},
			{5, 4, 8, 10, 8},
		},
		"incutId": 7,
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"wasBuyVip":      0,
			"vip_noSpecSeed": true,
		},
		"setVip_inFreeSpinAlways": -1,
		"helpseed":                true,
		"dynamitesFG": [][]int{
			{0, 0, 0, 0, 0},
			{0, 0, 0, 0, 0},
			{0, 0, 0, 0, 0},
		},
		"dynamitesXbet":    0,
		"dynamitesXbetCur": 0,
		"dynamitesWin":     0,
		"shotsList":        []any{},
		"bonus":            map[string]any{},
	}

	b, _ := json.Marshal(gs)
	return string(b)
}

func (m400154) InputCoef(ctl int32) int32 {
	switch ctl {
	//free
	case 1:
		return 12500
	//hot
	case 2:
		return 12500
	case 3:
		return 6075
	case 4:
		return 1035
	case 5:
		return 125
	default:
		return 100
	}
}

func (m m400154) MinPayout(ctl int32) int32 {
	return 0
}
