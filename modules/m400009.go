package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

type c400009 struct {
	GameID         int // 游戏ID
	RuleID         int // 生成器规则ID
	Row            int // 网格行数
	Column         int // 网格列数
	WildIcon       int16
	MaxPayout      int32
	NormIconWeight map[int16]int32
	Bornus2Prob    int
	Bornus3Prob    int
	Bornus2Range   int
	Bornus3Range   int
	Pattern        [][]basic.Position
	PayoutTable    map[int16][]int32
	ScatterIcon    int16
}

var _ = Factory.reg(basic.NewGeneral[*m400009])

type m400009 struct {
	Config                       c400009
	RandByNormIconWeight         *utils.RandomWeightPicker[int16, int32]
	RandByNormIconWithoutScatter *utils.RandomWeightPicker[int16, int32]
}

func (m *m400009) Init(config []byte) {
	m.Config = utils.ParseYAML[c400009](config)
	m.RandByNormIconWeight = utils.NewRandomWeightPicker(m.Config.NormIconWeight)
	normIconWithoutScatterWeight := make(map[int16]int32)
	for k, v := range m.Config.NormIconWeight {
		if k != m.Config.ScatterIcon {
			normIconWithoutScatterWeight[k] = v
		}
	}
	m.RandByNormIconWithoutScatter = utils.NewRandomWeightPicker(normIconWithoutScatterWeight)
}

func (m *m400009) genGrid(rd *rand.Rand) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)
	scatterCount := 0
	scatterInCol := make([]bool, m.Config.Column)
	for i := 0; i < m.Config.Row*m.Config.Column; i++ {
		col := i % m.Config.Column
		if scatterCount >= 3 || scatterInCol[col] {
			grid[i] = m.RandByNormIconWithoutScatter.One(rd)
		} else {
			icon := m.RandByNormIconWeight.One(rd)
			if icon == m.Config.ScatterIcon {
				scatterCount++
				scatterInCol[col] = true
			}
			grid[i] = icon
		}
	}
	return grid
}

func (m400009) ID() int32 {
	return 400009
}

func (m m400009) Line() int32 { // 线数
	return 9
}

func (m m400009) ClientMode() int32 {
	return basic.EnumClientMode.MULTI
}

func (m m400009) Exception(code int32) string {
	return "{}"
}

func (m *m400009) Spin(rd *rand.Rand) basic.ISpin {
	grid := m.genGrid(rd)
	spin := &games.S400009{}
	page := &games.P400009{
		Grid: grid,
	}
	page.Lines = m.parsePayout(grid, spin)
	spin.Pages = append(spin.Pages, *page)

	if spin.IsBonus2 {
		subGameInfo := m.genSubGameInfo(rd, "bonus2", int(page.Pays))
		spin.SubGameBonus2Info = append(spin.SubGameBonus2Info, subGameInfo)
		count := 0
		for i := 0; i < 5; i++ {
			if spin.SubGameBonus2Info[0].Av[i] != -1 {
				count++
			}
		}
		spin.Pays += int32(subGameInfo.CurWin)
		page.Pays = int32(subGameInfo.CurWin)
		spin.Pages = append(spin.Pages, *page)
		if count >= 4 {
			subGameInfo := m.genSubGameInfo(rd, "bonus3", int(page.Pays))
			spin.SubGameBonus3Info = append(spin.SubGameBonus3Info, subGameInfo)
			spin.Pays += int32(subGameInfo.CurWin)
			page.Pays = int32(subGameInfo.CurWin)
			spin.Pages = append(spin.Pages, *page)
		}
	}
	spin.SubGameDealerInfo = append(spin.SubGameDealerInfo, m.genSubGameInfo(rd, "dealer", int(page.Pays)))
	spin.SubGameRedBlackInfo = append(spin.SubGameRedBlackInfo, m.genSubGameInfo(rd, "redblack", int(page.Pays)))
	spin.Pages = append(spin.Pages, *page)
	return spin
}

func (m m400009) parsePayout(grid []int16, spin *games.S400009) []belatra.LinesInfo {
	gridRows := make([][]int16, m.Config.Row)

	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			val := grid[index]
			gridRows[row][col] = val
		}
	}

	// 检查是否触发Bonus
	scatterCount := 0
	for i := 0; i < len(gridRows); i++ {
		for j := 0; j < len(gridRows[i]); j++ {
			if gridRows[i][j] == m.Config.ScatterIcon {
				scatterCount++
			}
		}
	}
	if scatterCount == 3 {
		spin.IsBonus2 = true
	} else {
		spin.IsBonus2 = false
	}

	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 3 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = gridRows[row][col]
		}

		if ok, mul, payout, online := m.checkLine(symbols); ok {
			spin.Pays += payout * int32(mul)
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   int64(payout),
					K:      mul,
					Win:    int64(payout) * int64(mul),
					OnLine: online,
				},
			})
		}
	}

	return linesInfo
}

func (m m400009) checkLine(symbols []int16) (bool, int, int32, []int16) {
	if len(symbols) < 3 {
		return false, 1, 0, nil
	}

	count := 0
	maxIcon := int16(0)
	startIdx := 0

	for i := 0; i < len(symbols); i++ {
		curIcon := symbols[i]
		length := 1
		for j := i + 1; j < len(symbols); j++ {
			if symbols[j] == m.Config.ScatterIcon {
				break
			}
			if symbols[j] == curIcon || symbols[j] == m.Config.WildIcon {
				length++
			} else {
				break
			}
		}
		if length > count && length >= 3 {
			count = length
			maxIcon = curIcon
			startIdx = i
		}
	}

	if count >= 3 {
		payoutArr, ok := m.Config.PayoutTable[maxIcon]
		if !ok || len(payoutArr) <= count {
			return false, 1, 0, nil
		}
		payout := payoutArr[count]
		online := make([]int16, len(symbols))
		for i := 0; i < len(symbols); i++ {
			if i >= startIdx && i < startIdx+count {
				online[i] = symbols[i]
			} else {
				online[i] = 127
			}
		}
		mul := 1
		return true, mul, payout, online
	}
	return false, 1, 0, nil
}

func (m *m400009) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m400009) genSubGameInfo(rd *rand.Rand, game string, startWin int) belatra.SubGameInfo {
	switch game {
	case "bonus2":
		av := (rd.Intn(m.Config.Bornus2Range) + 1) * int(m.Line())
		avlist := []int{-1, -1, -1, -1, -1}
		for i := 0; i < 4; i++ {
			if rd.Intn(100) < m.Config.Bornus2Prob {
				avlist[i] = av
			} else {
				break
			}
		}
		curWin := 0
		for i := 0; i < 5; i++ {
			if avlist[i] != -1 {
				curWin += avlist[i]
			}
		}
		return belatra.SubGameInfo{
			Category:      "Bonus2",
			Type:          "2",
			StartWin:      0,
			PrevWin:       startWin,
			CurWin:        curWin,
			PaidWin:       curWin,
			Attempt:       0,
			Av:            avlist,
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "",
			Add:           nil,
			OnlyToBD:      nil,
			SendRestore:   nil,
			UserChoice:    "",
		}
	case "bonus3":
		return belatra.SubGameInfo{
			Category:      "Bonus3",
			Type:          "3",
			StartWin:      0,
			PrevWin:       0,
			CurWin:        2250,
			PaidWin:       2250,
			Attempt:       0,
			Av:            []int{1125, 1125, 0},
			AttemptResult: 0,
			WinLevel:      0,
			Rule:          "",
			Add:           nil,
			OnlyToBD:      nil,
			SendRestore:   nil,
			UserChoice:    "",
		}
	default:
		return belatra.SubGameInfo{}
	}
}

func (m *m400009) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400009)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	// 最大赔付
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}
	return s
}

func (m400009) Rule() string {
	gs := map[string]interface{}{
		"gamegroup":             "base",
		"doubleAssortment":      []string{"off", "dealer", "redblack"},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         []int{1, 2, 3, 5, 10, 15, 20, 25, 50, 100, 200, 300, 500},
		"denomAssortment_cents": []int{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]interface{}{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus":              nil,
		"outRatesVolatility":    nil,
		"placedbet":             0,
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            90,
		"betPerLine":            10,
		"nlines":                9,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 4500,
		"analInfo": map[string]interface{}{
			"formula": map[string]interface{}{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]interface{}{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles": [][]int{
				{1, 1, 1, 1, 1},
				{0, 0, 0, 0, 0},
				{2, 2, 2, 2, 2},
				{0, 1, 2, 1, 0},
				{2, 1, 0, 1, 2},
				{0, 0, 1, 0, 0},
				{2, 2, 1, 2, 2},
				{1, 2, 2, 2, 1},
				{1, 0, 0, 0, 1},
			},
			"symbolNames": []string{"", "crocodile", "seven", "caterpillar", "snail", "bar3", "bar2", "bar1", "frog"},
			"baseReels": [][]int{
				{7, 3, 8, 0, 3, 1, 7, 2, 3, 7, 0, 7, 6, 1, 8, 3, 0, 3, 7, 5, 2, 4, 3, 0},
				{6, 8, 0, 7, 1, 4, 6, 2, 3, 6, 4, 0, 6, 1, 4, 8, 6, 4, 2, 1, 2, 6, 4, 5},
				{8, 5, 6, 1, 0, 3, 7, 5, 0, 1, 7, 5, 8, 7, 5, 2, 8, 7, 5, 2, 8, 7, 5, 4},
				{3, 7, 0, 6, 8, 2, 1, 6, 0, 7, 6, 3, 6, 2, 4, 6, 1, 7, 6, 5, 6, 8, 1, 7},
				{6, 8, 4, 3, 1, 0, 3, 4, 7, 5, 3, 4, 7, 0, 4, 2, 8, 1, 3, 0, 4, 1, 3, 0},
			},
			"statTablo":           nil,
			"arrlimits_winLimitK": []int{1500},
			"wildIds":             []int{1},
			"scatterIds":          []int{8},
			"minScatters":         []int{3},
			"volatility":          3,
			"sasAdditionalId":     "PSW",
			"sasPaytableId":       "PSW920",
		},
		"helpInfo": map[string]interface{}{
			"paytable": [][]interface{}{
				{
					[]int{0, 2},
					[]int{5, 10000},
					[]int{4, 1000},
					[]int{4, 1000},
					[]int{3, 100},
					[]int{3, 100},
					[]int{3, 100},
				},
				{
					[]int{1, 1},
					[]int{5, 2500},
					[]int{4, 250},
					[]int{4, 250},
					[]int{3, 100},
					[]int{3, 100},
					[]int{3, 100},
				},
				{
					[]int{2, 4},
					[]int{5, 250},
					[]int{4, 100},
					[]int{4, 100},
					[]int{3, 20},
					[]int{3, 20},
					[]int{3, 20},
				},
				{
					[]int{3, 4},
					[]int{5, 150},
					[]int{4, 50},
					[]int{4, 50},
					[]int{3, 20},
					[]int{3, 20},
					[]int{3, 20},
				},
				{
					[]int{4, 4},
					[]int{5, 100},
					[]int{4, 50},
					[]int{4, 50},
					[]int{3, 10},
					[]int{3, 10},
					[]int{3, 10},
				},
				{
					[]int{5, 4},
					[]int{5, 75},
					[]int{4, 50},
					[]int{4, 50},
					[]int{3, 10},
					[]int{3, 10},
					[]int{3, 10},
				},
				{
					[]int{6, 4},
					[]int{5, 50},
					[]int{4, 10},
					[]int{4, 10},
					[]int{3, 5},
					[]int{3, 5},
					[]int{3, 5},
				},
				{
					[]int{7, 4},
					[]int{5, 20},
					[]int{4, 5},
					[]int{4, 5},
					[]int{3, 2},
					[]int{3, 2},
					[]int{3, 2},
				},
				{
					[]int{8, 8},
				},
			},
			"bonuses": []map[string]interface{}{
				{"att": 4, "max": 250},
				{"att": 8, "max": 200},
			},
			"doubles": [][]interface{}{
				{"off", 0, 0},
				{
					"dealer",
					5,
					100000,
					[]string{"openCardYes"},
				},
				{"redblack", 5, 100000},
			},
		},
		"doubleActive":           "dealer",
		"doubleActiveDbSettings": "dealer",
		"antiDynamiteBet":        45,
		"dramshow":               nil,
		"versions": map[string]interface{}{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]interface{}{
			"maxWinLimitK":         1500,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 1500,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []int{9},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox": [][]int{
			{0, 1, 5, 6, 0},
			{3, 6, 2, 0, 3},
			{7, 4, 7, 7, 4},
		},
		"stopBox": [][]int{
			{0, 1, 5, 6, 0},
			{3, 6, 2, 0, 3},
			{7, 4, 7, 7, 4},
		},
		"newOpenGames": []interface{}{},
		"info_adv_games": map[string]interface{}{
			"list": []interface{}{},
			"hint": 0,
			"nnew": 0,
		}}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (m400009) InputCoef(int32) int32 {
	return 100
}

func (m m400009) MinPayout(int32) int32 {
	return 0
}
