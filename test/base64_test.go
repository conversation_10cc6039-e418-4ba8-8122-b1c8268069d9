package test

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"testing"
)

func CompressJSON(b []byte) (string, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	if _, err := writer.Write(b); err != nil {
		log.Printf("Gzip compression error: %v", err)
		writer.Close()
		return "", err
	}
	if err := writer.Close(); err != nil {
		log.Printf("Gzip writer close error: %v", err)
		return "", err
	}
	compressedBase64 := base64.StdEncoding.EncodeToString(buf.Bytes())
	return compressedBase64, nil
}

// DecompressJSON 将Base64编码的Gzip压缩数据解码并解压缩为原始JSON字节切片
func DecompressJSON(compressedBase64 string) ([]byte, error) {
	// 1. Base64解码
	compressedBytes, err := base64.StdEncoding.DecodeString(compressedBase64)
	if err != nil {
		log.Printf("Base64 decode error: %v", err)
		return nil, err
	}

	// 2. 创建Gzip读取器
	reader, err := gzip.NewReader(bytes.NewReader(compressedBytes))
	if err != nil {
		log.Printf("Gzip reader creation error: %v", err)
		return nil, err
	}
	defer reader.Close()

	// 3. 读取解压缩后的数据
	var buf bytes.Buffer
	if _, err := io.Copy(&buf, reader); err != nil {
		log.Printf("Gzip decompression error: %v", err)
		return nil, err
	}

	// 4. 返回解压缩后的字节切片
	return buf.Bytes(), nil
}

func TestBase64(t *testing.T) {
	encoded := "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"

	b, _ := DecompressJSON(encoded)
	fmt.Println(string(b))
}
