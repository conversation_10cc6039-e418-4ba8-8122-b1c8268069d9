package main

import (
	"flag"
	"fmt"
	"igame"
	"igameCommon/basic"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// TestResult 存储单次测试的结果
type TestResult struct {
	SpinIndex         int
	OriginalPayout    int32
	RegenerateIndex   int
	SaltSeed          int64
	Success           bool
	RegeneratedPayout int32
	ErrorMessage      string
	OriginalSpin      basic.ISpin
	RegeneratedSpin   basic.ISpin
}

// TestStats 存储测试统计信息
type TestStats struct {
	TotalTests   int64
	WinningSpins int64
	SuccessCount int64
	FailureCount int64
	StartTime    time.Time
	EndTime      time.Time
}

// WorkerTask 工作任务
type WorkerTask struct {
	SpinIndex int
	SpinSeed  int64
	GameID    int32
}

func main() {
	// 命令行参数
	var gameID = flag.Int("game", 400163, "游戏ID")
	var ctl = flag.Int("ctl", 0, "算法模式 (default 0)")
	var testCount = flag.Int("tests", 10000, "测试次数")
	var workers = flag.Int("workers", runtime.NumCPU(), "并发工作者数量")
	var regenerateCount = flag.Int("regenerates", 10, "每次旋转的再生测试次数")
	var verbose = flag.Bool("verbose", false, "详细输出模式")
	var progressInterval = flag.Int("progress", 1000, "进度报告间隔")

	flag.Parse()

	fmt.Printf("=== M%d 随机再生功能并发测试 ===\n", *gameID)
	fmt.Printf("算法模式: %d\n", *ctl)
	fmt.Printf("测试次数: %d\n", *testCount)
	fmt.Printf("并发 Worker: %d\n", *workers)
	fmt.Printf("每次再生测试: %d\n", *regenerateCount)
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	fmt.Printf("详细模式: %v\n", *verbose)
	fmt.Printf("=====================================\n\n")

	// 初始化游戏模块
	igame.InitFromSnapshoot(int32(*gameID), 0)

	// 创建统计对象
	stats := &TestStats{
		StartTime: time.Now(),
	}

	// 创建任务通道和结果通道
	taskChan := make(chan WorkerTask, *workers*2)
	resultChan := make(chan TestResult, *workers*2)

	// 启动工作者
	var wg sync.WaitGroup
	for i := 0; i < *workers; i++ {
		wg.Add(1)
		go worker(i, taskChan, resultChan, &wg, int32(*gameID), int32(*ctl), *regenerateCount, *verbose)
	}

	// 启动结果收集器
	var resultWg sync.WaitGroup
	resultWg.Add(1)
	go resultCollector(resultChan, &resultWg, stats, *verbose, *progressInterval)

	// 生成任务
	go func() {
		defer close(taskChan)
		for i := 0; i < *testCount; i++ {
			task := WorkerTask{
				SpinIndex: i,
				SpinSeed:  int64(i + 1000),
				GameID:    int32(*gameID),
			}
			taskChan <- task
		}
	}()

	// 等待所有工作者完成
	wg.Wait()
	close(resultChan)

	// 等待结果收集完成
	resultWg.Wait()

	// 输出最终统计
	stats.EndTime = time.Now()
	printFinalStats(stats)
}

// worker 工作者函数
func worker(workerID int, taskChan <-chan WorkerTask, resultChan chan<- TestResult, wg *sync.WaitGroup, gameID, ctl int32, regenerateCount int, verbose bool) {
	defer wg.Done()

	for task := range taskChan {
		// 生成旋转结果
		spin, _ := igame.Spin(gameID, ctl, task.SpinSeed, 0.0)
		payout := spin.Payout()

		// 只测试有中奖的旋转
		if payout > 0 {
			// 进行多次随机再生测试
			for j := 0; j < regenerateCount; j++ {
				saltSeed := task.SpinSeed + int64(j*1000)
				result := testRegenerate(task, spin, saltSeed, j, verbose)
				resultChan <- result
			}
		}
	}
}

// testRegenerate 执行单次随机再生测试
func testRegenerate(task WorkerTask, spin basic.ISpin, saltSeed int64, regenerateIndex int, verbose bool) TestResult {
	result := TestResult{
		SpinIndex:       task.SpinIndex,
		OriginalPayout:  spin.Payout(),
		RegenerateIndex: regenerateIndex,
		SaltSeed:        saltSeed,
	}
	result.OriginalSpin = spin

	// 执行随机再生
	module := igame.GetModuleFromSnapshoot(task.GameID)
	regeneratedSpin := module.Salting(spin, saltSeed)
	regeneratedPayout := regeneratedSpin.Payout()
	result.RegeneratedSpin = regeneratedSpin

	result.RegeneratedPayout = regeneratedPayout
	result.Success = (regeneratedPayout == result.OriginalPayout)

	if !result.Success {
		result.ErrorMessage = fmt.Sprintf("赔付不一致: 原始=%d, 再生=%d", result.OriginalPayout, regeneratedPayout)
	}

	return result
}

// resultCollector 结果收集器
func resultCollector(resultChan <-chan TestResult, wg *sync.WaitGroup, stats *TestStats, verbose bool, progressInterval int) {
	defer wg.Done()

	var processedCount int64
	var lastProgressTime = time.Now()

	for result := range resultChan {
		atomic.AddInt64(&stats.TotalTests, 1)

		if result.RegenerateIndex == 0 {
			atomic.AddInt64(&stats.WinningSpins, 1)
		}

		if result.Success {
			atomic.AddInt64(&stats.SuccessCount, 1)
			if verbose {
				fmt.Printf("\n=== 随机再生成功 ===\n")
				fmt.Printf("旋转序号: %d, 再生序号: %d\n", result.SpinIndex, result.RegenerateIndex)
				fmt.Printf("原始游戏数据: %+v", result.OriginalSpin)
				fmt.Printf("再生游戏数据: %+v", result.RegeneratedSpin)
				fmt.Printf("====================\n\n")
			}
		} else {
			atomic.AddInt64(&stats.FailureCount, 1)

			// 输出失败详情
			fmt.Printf("\n=== 随机再生失败 ===\n")
			fmt.Printf("旋转序号: %d, 再生序号: %d\n", result.SpinIndex, result.RegenerateIndex)
			fmt.Printf("盐值种子: %d\n", result.SaltSeed)
			fmt.Printf("错误信息: %s\n", result.ErrorMessage)

			if verbose {
				fmt.Printf("原始游戏数据: %+v", result.OriginalSpin)
				fmt.Printf("再生游戏数据: %+v", result.RegeneratedSpin)
			}
			fmt.Printf("====================\n\n")
		}

		processedCount++

		// 定期输出进度
		if processedCount%int64(progressInterval) == 0 || time.Since(lastProgressTime) > 5*time.Second {
			printProgress(stats, processedCount)
			lastProgressTime = time.Now()
		}
	}
}

// printProgress 打印进度信息
func printProgress(stats *TestStats, processedCount int64) {
	elapsed := time.Since(stats.StartTime)
	rate := float64(processedCount) / elapsed.Seconds()

	fmt.Printf("进度: 已处理 %d 次测试, 中奖旋转 %d, 成功 %d, 失败 %d, 速度 %.1f tests/sec\n",
		processedCount,
		atomic.LoadInt64(&stats.WinningSpins),
		atomic.LoadInt64(&stats.SuccessCount),
		atomic.LoadInt64(&stats.FailureCount),
		rate)
}

// printFinalStats 打印最终统计信息
func printFinalStats(stats *TestStats) {
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Printf("\n=== 最终测试结果 ===\n")
	fmt.Printf("总测试时间: %v\n", duration)
	fmt.Printf("总测试次数: %d\n", stats.TotalTests)
	fmt.Printf("中奖旋转数量: %d\n", stats.WinningSpins)
	fmt.Printf("再生测试成功: %d\n", stats.SuccessCount)
	fmt.Printf("再生测试失败: %d\n", stats.FailureCount)

	if stats.TotalTests > 0 {
		successRate := float64(stats.SuccessCount) / float64(stats.TotalTests) * 100
		fmt.Printf("成功率: %.2f%%\n", successRate)

		avgRate := float64(stats.TotalTests) / duration.Seconds()
		fmt.Printf("平均测试速度: %.1f tests/sec\n", avgRate)
	}

	if stats.FailureCount == 0 {
		fmt.Printf("✓ 所有随机再生测试均通过！\n")
	} else {
		fmt.Printf("✗ 发现 %d 次随机再生失败！\n", stats.FailureCount)
	}
	fmt.Printf("===================\n")
}
