#!/usr/bin/env python3
import os
import subprocess
import sys

def run_command(command):
    try:
        subprocess.run(command, shell=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        return False

def check_upx_available():
    """检查UPX是否可用"""
    try:
        result = subprocess.run("upx --version", shell=True, capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def compress_with_upx():
    """使用UPX压缩可执行文件"""
    # 检测 UPX 命令：优先使用系统安装，其次使用本地 tools 目录
    if check_upx_available():
        upx_cmd = "upx"
    else:
        upx_local_win = os.path.abspath(os.path.join("tools", "upx.exe"))
        upx_local_unix = os.path.abspath(os.path.join("tools", "upx"))
        if os.path.exists(upx_local_win):
            upx_cmd = upx_local_win
        elif os.path.exists(upx_local_unix):
            upx_cmd = upx_local_unix
        else:
            print("UPX未安装或不可用，跳过压缩步骤")
            return True
    
    print("开始使用UPX压缩可执行文件...")
    exe_files = ["bin/generate.exe", "bin/paytab.exe", "bin/tax.exe"]
    
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            print(f"压缩 {exe_file}...")
            if not run_command(f'{upx_cmd} --best --lzma "{exe_file}"'):
                print(f"压缩 {exe_file} 失败")
                return False
        else:
            print(f"文件 {exe_file} 不存在，跳过")
    
    print("UPX压缩完成")
    return True

def main():
    # 构建可执行文件
    print("开始构建可执行文件...")
    
        # 检测操作系统
    import platform
    is_windows = platform.system() == "Windows"
    
    if is_windows:
        # Windows环境下的构建命令
        build_commands = [
            "go env -w GOOS=windows GOARCH=amd64 && go build -ldflags="-s -w" -tags exe -o bin/generate.exe cmd/generate/main.go",
            "go env -w GOOS=windows GOARCH=amd64 && go build -ldflags="-s -w" -tags exe -o bin/paytab.exe cmd/paytab/main.go",
            "go env -w GOOS=windows GOARCH=amd64 && go build -ldflags="-s -w" -tags exe -o bin/tax.exe cmd/tax/main.go"
        ]
    else:
        # Linux/Mac环境下的构建命令
        build_commands = [
            "GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -tags exe -o bin/generate.exe cmd/generate/main.go",
            "GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -tags exe -o bin/paytab.exe cmd/paytab/main.go",
            "GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -tags exe -o bin/tax.exe cmd/tax/main.go"
        ]
    
    for cmd in build_commands:
        if not run_command(cmd):
            print("构建失败")
            sys.exit(1)
    print("构建成功")

    # 使用UPX压缩
    # if not compress_with_upx():
    #     print("UPX压缩失败")
    #     sys.exit(1)

    # 进入 bin 目录
    os.chdir("bin")
    
    # 执行 git pull
    print("开始更新仓库...")
    if not run_command("git pull"):
        print("更新仓库失败，取消提交")
        sys.exit(1)
    print("仓库更新成功")

    # 添加所有更改
    if not run_command("git add ."):
        print("添加文件失败")
        sys.exit(1)

    # 提交更改
    if not run_command('git commit -m "Automatic submission"'):
        print("提交失败")
        sys.exit(1)

    # 推送到远程仓库
    if not run_command("git push"):
        print("推送失败")
        sys.exit(1)

    print("所有操作已完成")

if __name__ == "__main__":
    main() 