#!/usr/bin/env python3
import os
import subprocess
import sys

def run_command(command, cwd=None):
    """执行命令并返回是否成功"""
    try:
        subprocess.run(command, shell=True, check=True, cwd=cwd)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        return False

def update_repo(repo_name, repo_url, repo_dir):
    """更新或克隆单个仓库"""
    print(f"\n=== 处理 {repo_name} 仓库 ===")
    
    if not os.path.exists(repo_dir):
        print(f"{repo_name} 目录不存在，开始克隆仓库...")
        if not run_command(f"git clone {repo_url} {repo_dir}"):
            print(f"克隆 {repo_name} 仓库失败")
            return False
        print(f"{repo_name} 仓库克隆成功")
    else:
        print(f"{repo_name} 目录已存在，开始更新...")
        if not run_command("git pull", cwd=repo_dir):
            print(f"更新 {repo_name} 仓库失败")
            return False
        print(f"{repo_name} 仓库更新成功")
    
    # 检查是否有go.mod文件
    go_mod_path = os.path.join(repo_dir, "go.mod")
    if os.path.exists(go_mod_path):
        print(f"在 {repo_name} 目录中执行 go mod tidy...")
        if not run_command("go mod tidy", cwd=repo_dir):
            print(f"在 {repo_name} 目录中执行 go mod tidy 失败")
            return False
        print(f"{repo_name} go mod tidy 执行成功")
    else:
        print(f"{repo_name} 目录中没有 go.mod 文件，跳过 go mod tidy")
    
    return True

def main():
    # 仓库配置
    repos = {
        "bin": {
            "url": "**************:jfcwrlight/igame.git",
            "dir": "bin"
        },
        "common": {
            "url": "**************:jfcwrlight/igameCommon.git", 
            "dir": "common"
        },
        "http": {
            "url": "**************:jfcwrlight/igameHttp.git",
            "dir": "http"
        }
    }
    
    print("开始更新所有仓库...")
    
    # 更新每个仓库
    for repo_name, repo_info in repos.items():
        if not update_repo(repo_name, repo_info["url"], repo_info["dir"]):
            print(f"更新 {repo_name} 失败，退出程序")
            sys.exit(1)
    
    # 在主目录也执行 go mod tidy
    print("\n=== 在主目录执行 go mod tidy ===")
    if os.path.exists("go.mod"):
        if not run_command("go mod tidy"):
            print("在主目录执行 go mod tidy 失败")
            sys.exit(1)
        print("主目录 go mod tidy 执行成功")
    else:
        print("主目录中没有 go.mod 文件，跳过 go mod tidy")
    
    print("\n所有仓库更新完成！")

if __name__ == "__main__":
    main() 